# 离线触达批量流控分析报告

## 1. 概述

离线触达批量流控是麻雀CDP系统中专门用于离线普通触达场景的频控机制，通过`FlowCtrlCoreServiceImpl.flowCtrl()`方法实现批量用户的流控过滤，确保用户不会在指定时间窗口内收到过多的营销触达。

## 2. 完整调用链路图

```mermaid
graph TD
    A[离线普通触达任务启动] --> B[AbstractStrategyDispatchService.coreLogicExecute]
    B --> C[queryAndGroupAndSend 分页查询+分组+下发]
    C --> D[batchQueryAndSend 批量查询和发送]
    D --> E[dispatchHandler 执行下发]
    E --> F[executeDispatch 执行下发逻辑]
    F --> G[具体实现类.dispatchHandler]
    G --> H[BatchDispatchServiceImpl.sendXxx方法]
    H --> I[AbstractDispatchService.request]
    I --> J[DispatchFlcService.batchDispatchFlcLockThenReturnExcludeUsers]
    J --> K[并发执行单用户流控]
    K --> L[tryLockSingle 单用户流控]
    L --> M[FlowCtrlCoreServiceImpl.flowCtrl 批量流控核心]
    M --> N[UserDispatchDetailService.getUserIndex/getUserIndexNew]
    N --> O[查询用户触达指标]
    O --> P[interception 流控判断]
    P --> Q[FlowCtrlInterceptionLogService.saveInterceptionLog]
    Q --> R[返回通过流控的用户列表]
```

## 3. 关键类和方法对应关系

### 3.1 核心流控类

| 类名 | 方法 | 功能 | 代码位置 |
|------|------|------|----------|
| FlowCtrlCoreServiceImpl | flowCtrl() | 批量流控核心逻辑 | cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java:70 |
| FlowCtrlCoreServiceImpl | execute() | 旧版流控执行逻辑 | cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java:154 |
| FlowCtrlCoreServiceImpl | newExecute() | 新版流控执行逻辑 | cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java:107 |
| FlowCtrlCoreServiceImpl | interception() | 流控判断逻辑 | cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlCoreServiceImpl.java:189 |

### 3.2 触达指标查询类

| 类名 | 方法 | 功能 | 代码位置 |
|------|------|------|----------|
| UserDispatchDetailServiceImpl | getUserIndex() | 获取用户流控指标(旧版) | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/impl/UserDispatchDetailServiceImpl.java:169 |
| UserDispatchDetailServiceImpl | getUserIndexNew() | 获取用户流控指标(新版) | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/impl/UserDispatchDetailServiceImpl.java:193 |
| UserDispatchDetailServiceImpl | queryIndex() | 从数仓查询用户触达指标 | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/impl/UserDispatchDetailServiceImpl.java:215 |
| UserDispatchDetailServiceImpl | getLocalIndexMap() | 从本地库查询当天触达次数 | cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/impl/UserDispatchDetailServiceImpl.java:282 |

### 3.3 批量流控调度类

| 类名 | 方法 | 功能 | 代码位置 |
|------|------|------|----------|
| DispatchFlcService | batchDispatchFlcLockThenReturnExcludeUsers() | 离线批量流控入口 | cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/DispatchFlcService.java:181 |
| DispatchFlcService | tryLockSingle() | 单用户流控处理 | cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/DispatchFlcService.java:295 |
| AbstractDispatchService | request() | 批量下发请求处理 | cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/impl/AbstractDispatchService.java:91 |

### 3.4 流控日志记录类

| 类名 | 方法 | 功能 | 代码位置 |
|------|------|------|----------|
| FlowCtrlInterceptionLogServiceImpl | saveInterceptionLog() | 保存拦截日志(旧版) | cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlInterceptionLogServiceImpl.java:44 |
| FlowCtrlInterceptionLogServiceImpl | saveInterceptionLogNew() | 保存拦截日志(新版) | cdp-domain/src/main/java/com/xftech/cdp/domain/flowctrl/service/impl/FlowCtrlInterceptionLogServiceImpl.java:71 |

## 4. 方法调用时序图

```mermaid
sequenceDiagram
    participant A as AbstractStrategyDispatchService
    participant B as BatchDispatchServiceImpl
    participant C as AbstractDispatchService
    participant D as DispatchFlcService
    participant E as FlowCtrlCoreServiceImpl
    participant F as UserDispatchDetailServiceImpl
    participant G as FlowCtrlInterceptionLogServiceImpl
    participant H as Database

    A->>B: dispatchHandler(策略上下文, 用户批次)
    B->>C: request(渠道类型, 下发参数, 用户列表)
    C->>D: batchDispatchFlcLockThenReturnExcludeUsers()
    
    Note over D: 并发处理每个用户
    loop 每个用户
        D->>D: tryLockSingle(用户详情)
        D->>E: flowCtrl(流控DTO, 状态列表)
        
        Note over E: 根据Apollo开关选择新旧流控
        alt 新流控开关开启
            E->>E: newExecute(流控DTO, 状态列表)
        else 旧流控逻辑
            E->>E: execute(流控DTO, 状态列表)
        end
        
        E->>F: getUserIndex/getUserIndexNew(表序号, 流控规则, 用户列表)
        F->>H: 查询用户触达指标
        H-->>F: 返回触达次数统计
        F-->>E: 返回用户指标DTO列表
        
        E->>E: interception(流控规则, 用户指标)
        
        alt 用户被流控
            E->>G: saveInterceptionLog(流控DTO, 拦截用户列表)
            G->>H: 保存拦截日志
        end
        
        E-->>D: 返回通过流控的用户列表
    end
    
    D-->>C: 返回批量流控结果DTO
    C->>C: filterBatchParamsList(排除被流控用户)
    C->>C: 执行实际下发请求
    C-->>B: 返回下发结果
    B-->>A: 返回处理结果
```

## 5. 数据流分析

### 5.1 流控规则获取
- **来源**: `CacheFlowCtrlSerivce.getFlowCtrlConfig()`
- **缓存**: Apollo配置 + Redis缓存
- **规则类型**: 渠道流控、策略流控、多策略共享流控、业务线流控

### 5.2 用户触达指标查询
- **本地查询**: 查询当天`user_dispatch_detail_*`表的触达记录
- **数仓查询**: 查询历史多天的触达统计数据
- **特征平台**: 新版流控使用特征平台接口获取用户历史触达次数

### 5.3 流控判断逻辑
```java
// 流控判断核心逻辑
private boolean interception(FlowCtrlDo rule, UserDispatchIndexDto indexDto) {
    return Objects.nonNull(rule.getLimitTimes()) && indexDto.getCount() >= rule.getLimitTimes();
}
```

### 5.4 数据表操作

| 操作类型 | 表名 | 操作时机 | 说明 |
|----------|------|----------|------|
| 查询 | user_dispatch_detail_* | 流控检查时 | 查询用户当天触达次数 |
| 插入 | flow_ctrl_interception_log | 用户被流控时 | 记录流控拦截日志 |
| 插入 | user_dispatch_detail_* | 触达成功后 | 记录用户触达明细 |
| 插入 | crowd_push_batch | 批次处理时 | 记录批次推送信息 |

## 6. 流控配置和开关

### 6.1 Apollo配置开关
- `newFlowCtrlSwitch`: 新版流控开关，按策略ID灰度
- `newFlowCtrlRuleSwitch`: 新版流控规则开关
- `batchDispatchFlcLockSwitch`: 批量流控总开关，按渠道配置

### 6.2 流控规则类型
- **渠道流控**: 按渠道限制触达频次
- **策略流控**: 按策略限制触达频次  
- **多策略共享**: 多个策略共享流控额度
- **业务线流控**: 按业务线限制触达频次

## 7. 性能优化机制

### 7.1 并发处理
- 使用线程池`DispatchFlcExecutor.getPool()`并发处理每个用户的流控检查
- 减少单用户流控检查的总耗时

### 7.2 缓存优化
- 流控规则缓存在Redis中，避免重复查询数据库
- 用户触达指标支持Redis缓存优化(TODO项)

### 7.3 分批查询
- 用户触达指标查询按`strategyConfig.getDetailBatchSize()`分批进行
- 避免大批量查询对数据库造成压力

## 8. 监控和日志

### 8.1 流控拦截日志
- **表名**: `flow_ctrl_interception_log`
- **记录内容**: 用户ID、流控规则ID、策略ID、渠道ID、拦截时间等
- **统计功能**: 支持按策略、渠道统计流控拦截数据

### 8.2 性能监控
- 记录流控执行耗时: `log.info("流量控制-当前批次用户数：{}，拦截用户数：{}，耗时：{}ms")`
- 记录流控规则执行情况和拦截统计

## 9. 总结

离线触达批量流控通过`FlowCtrlCoreServiceImpl.flowCtrl()`实现，具有以下特点：

1. **批量处理**: 支持批量用户的流控检查，提高处理效率
2. **灵活配置**: 支持多种流控规则类型和动态配置
3. **性能优化**: 采用并发处理、缓存机制和分批查询优化性能
4. **完善监控**: 提供详细的流控日志记录和性能监控
5. **渐进升级**: 支持新旧版本流控逻辑的平滑切换

该机制确保了离线触达场景下用户不会收到过多的营销信息，同时保持了系统的高性能和可维护性。
